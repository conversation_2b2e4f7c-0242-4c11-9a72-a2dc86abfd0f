function [residual, T_order, T] = dynamic_resid(y, x, params, steady_state, T_order, T)
if nargin < 6
    T_order = -1;
    T = NaN(0, 1);
end
[T_order, T] = model_identification_simplified.sparse.dynamic_resid_tt(y, x, params, steady_state, T_order, T);
residual = NaN(3, 1);
    residual(1) = (y(4)) - (0.8*y(1)+0.3*y(5)+0.2*y(6)+x(1));
    residual(2) = (y(6)) - (0.6*y(3)+0.4*y(2)+y(4)*0.2+x(3));
    residual(3) = (y(5)) - (y(2)*0.7+y(4)*0.3+y(6)*0.1+x(2));
end
