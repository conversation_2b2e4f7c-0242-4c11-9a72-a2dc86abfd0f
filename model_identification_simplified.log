Starting Dynare (version 6.3).
Calling Dynare with arguments: noclearall
Starting preprocessing of the model file ... 
Found 3 equation(s). 
Evaluating expressions... 
Computing static model derivatives (order 2). 
Computing static model derivatives w.r.t. parameters (order 2). 
Normalizing the static model... 
Finding the optimal block decomposition of the static model... 
1 block(s) found: 
  0 recursive block(s) and 1 simultaneous block(s). 
  the largest simultaneous block has 3 equation(s) 
                                 and 3 feedback variable(s). 
Computing dynamic model derivatives (order 2). 
Computing dynamic model derivatives w.r.t. parameters (order 2). 
Normalizing the dynamic model... 
Finding the optimal block decomposition of the dynamic model... 
1 block(s) found: 
  0 recursive block(s) and 1 simultaneous block(s). 
  the largest simultaneous block has 3 equation(s) 
                                 and 1 feedback variable(s). 
Preprocessing completed. 
Preprocessing time: 0h00m02s.

STEADY-STATE RESULTS:

n 		 -1.11022e-16
e 		 -1.66533e-16
d 		 -3.88578e-16

EIGENVALUES:
         Modulus             Real        Imaginary
          0.5401           0.5382          0.04487
          0.5401           0.5382         -0.04487
           1.333            1.333                0

There are 1 eigenvalue(s) larger than 1 in modulus for 0 forward-looking variable(s)
The rank condition ISN'T verified!

