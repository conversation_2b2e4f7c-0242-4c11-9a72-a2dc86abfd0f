%% 运行模型识别性检验的MATLAB脚本
%% 请确保已安装Dynare并正确配置路径

clear all;
close all;
clc;

fprintf('=== 开始模型识别性检验 ===\n\n');

%% 1. 运行简化线性模型
fprintf('1. 运行简化线性模型识别性检验...\n');
try
    dynare model_identification_simplified.mod noclearall;
    fprintf('   ✓ 简化模型识别性检验完成\n\n');
catch ME
    fprintf('   ✗ 简化模型识别性检验失败: %s\n\n', ME.message);
end

%% 2. 运行非线性模型
fprintf('2. 运行非线性模型识别性检验...\n');
try
    dynare model_nonlinear_identification.mod noclearall;
    fprintf('   ✓ 非线性模型识别性检验完成\n\n');
catch ME
    fprintf('   ✗ 非线性模型识别性检验失败: %s\n\n', ME.message);
end

%% 3. 运行完整模型（如果前面的测试成功）
fprintf('3. 运行完整模型识别性检验...\n');
try
    dynare model_identification.mod noclearall;
    fprintf('   ✓ 完整模型识别性检验完成\n\n');
catch ME
    fprintf('   ✗ 完整模型识别性检验失败: %s\n\n', ME.message);
end

%% 4. 总结和建议
fprintf('=== 识别性检验总结 ===\n');
fprintf('请查看上述输出结果，特别注意以下几点：\n\n');
fprintf('1. 参数识别性状态：\n');
fprintf('   - "identified": 参数可识别\n');
fprintf('   - "unidentified": 参数不可识别\n');
fprintf('   - "weakly identified": 参数弱识别\n\n');

fprintf('2. 模型稳定性：\n');
fprintf('   - 检查特征值是否满足Blanchard-Kahn条件\n');
fprintf('   - 确保没有单位根或爆炸性路径\n\n');

fprintf('3. 如果识别性存在问题，建议：\n');
fprintf('   - 检查模型方程的数学形式是否正确\n');
fprintf('   - 调整参数的初始值或约束\n');
fprintf('   - 考虑简化模型结构\n');
fprintf('   - 增加更多的观测变量或方程\n\n');

fprintf('4. 数值稳定性：\n');
fprintf('   - 如果稳态求解失败，尝试不同的初始值\n');
fprintf('   - 检查参数值是否在合理范围内\n');
fprintf('   - 考虑使用不同的求解算法\n\n');

fprintf('=== 检验完成 ===\n');
