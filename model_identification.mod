// Dynare模型文件：检测模型可识别性
// 基于提供的三方程均衡模型

// 声明变量
var n, e, d;           // 内生变量：生育率、教育水平、消费
var n_lag, e_lag;      // 滞后变量

// 声明参数
parameters Phi, varphi, epsilon, psi, mu, v_n, v_e, alpha, beta;

// 参数赋值（用于识别性检验的基准值）
Phi = 0.1;        // 子女抚育固定成本
varphi = 0.05;    // 教育成本系数
epsilon = 2.0;    // 教育成本弹性
psi = 0.02;       // 父母赡养成本系数
mu = 1.5;         // 父母赡养成本弹性
v_n = 1.0;        // 生育偏好参数
v_e = 1.0;        // 教育偏好参数
alpha = 0.3;      // 效用函数参数
beta = 0.95;      // 贴现因子

// 模型方程
model;
    // 方程1：最优化条件1
    (1 - e_lag - d) / (beta * d) * (1 - (Phi + varphi * e^epsilon) * n - psi * n_lag^mu / mu) = 
    epsilon / v_e * varphi * e^epsilon * n * d;
    
    // 方程2：最优化条件2
    d * ((1 + beta) * d - 1 + e_lag) = 
    alpha / (1 - alpha) * mu / (psi * n^mu) * ((1 + beta) * (1 - e_lag) - (1 + 2*beta) * d);
    
    // 方程3：最优化条件3
    (Phi + (1 - v_n/v_e * epsilon) * varphi * e^epsilon) * d = 
    ((1 + beta) * (1 - e_lag) - (1 + 2*beta) * d) / beta * 
    (1 - (Phi + varphi * e^epsilon) * n - psi * n_lag^mu / mu) * mu / n;
    
    // 定义滞后变量
    n_lag = n(-1);
    e_lag = e(-1);
end;

// 初始值设定
initval;
    n = 0.5;
    e = 0.3;
    d = 0.4;
    n_lag = 0.5;
    e_lag = 0.3;
end;

// 稳态计算
steady;

// 检查Blanchard-Kahn条件
check;

// 模型识别性检验
identification;

// 计算脉冲响应函数（如果模型可识别）
stoch_simul(order=1, irf=40);

// 输出结果说明
disp('=== 模型识别性检验结果 ===');
disp('如果上述identification命令成功执行且没有错误，说明模型在给定参数值下是可识别的');
disp('如果出现错误或警告，请检查：');
disp('1. 模型方程是否正确线性化');
disp('2. 参数值是否合理');
disp('3. 是否存在共线性问题');
disp('4. 变量数量是否与方程数量匹配');

// 敏感性分析：检验不同参数值下的识别性
disp('=== 参数敏感性分析 ===');

// 保存原始参数值
Phi_orig = Phi;
beta_orig = beta;
alpha_orig = alpha;

// 测试不同的beta值
for beta_test = [0.9, 0.95, 0.99]
    beta = beta_test;
    try
        steady;
        identification(silent);
        fprintf('beta = %.2f: 模型可识别\n', beta_test);
    catch
        fprintf('beta = %.2f: 模型识别性存在问题\n', beta_test);
    end
end

// 测试不同的alpha值
beta = beta_orig;  // 恢复原值
for alpha_test = [0.2, 0.3, 0.4, 0.5]
    alpha = alpha_test;
    try
        steady;
        identification(silent);
        fprintf('alpha = %.2f: 模型可识别\n', alpha_test);
    catch
        fprintf('alpha = %.2f: 模型识别性存在问题\n', alpha_test);
    end
end

// 恢复原始参数值
alpha = alpha_orig;
Phi = Phi_orig;
beta = beta_orig;

disp('=== 识别性检验完成 ===');
