function [residual, T_order, T] = static_resid(y, x, params, T_order, T)
if nargin < 5
    T_order = -1;
    T = NaN(0, 1);
end
[T_order, T] = model_identification_simplified.sparse.static_resid_tt(y, x, params, T_order, T);
residual = NaN(3, 1);
    residual(1) = (y(1)) - (y(1)*0.8+0.3*y(2)+0.2*y(3)+x(1));
    residual(2) = (y(3)) - (y(3)*0.6+y(2)*0.4+y(1)*0.2+x(3));
    residual(3) = (y(2)) - (y(2)*0.7+y(1)*0.3+y(3)*0.1+x(2));
end
