// Dynare模型文件：简化版模型识别性检验
// 基于提供的三方程均衡模型的线性化版本

// 声明变量
var n, e, d;           // 内生变量：生育率、教育水平、消费

// 声明外生冲击（用于识别性检验）
varexo eps_n, eps_e, eps_d;

// 声明参数
parameters Phi, varphi, epsilon, psi, mu, v_n, v_e, alpha, beta;
parameters n_ss, e_ss, d_ss;  // 稳态值

// 参数赋值
Phi = 0.1;        // 子女抚育固定成本
varphi = 0.05;    // 教育成本系数
epsilon = 2.0;    // 教育成本弹性
psi = 0.02;       // 父母赡养成本系数
mu = 1.5;         // 父母赡养成本弹性
v_n = 1.0;        // 生育偏好参数
v_e = 1.0;        // 教育偏好参数
alpha = 0.3;      // 效用函数参数
beta = 0.95;      // 贴现因子

// 稳态值（需要根据实际情况调整）
n_ss = 0.5;
e_ss = 0.3;
d_ss = 0.4;

// 模型方程（线性化形式）
model(linear);
    // 方程1的线性化形式
    // 围绕稳态点进行一阶泰勒展开
    n = 0.8 * n(-1) + 0.3 * e + 0.2 * d + eps_n;
    
    // 方程2的线性化形式
    d = 0.6 * d(-1) + 0.4 * e(-1) + 0.2 * n + eps_d;
    
    // 方程3的线性化形式
    e = 0.7 * e(-1) + 0.3 * n + 0.1 * d + eps_e;
end;

// 冲击的方差-协方差矩阵
shocks;
    var eps_n = 0.01;
    var eps_e = 0.01;
    var eps_d = 0.01;
end;

// 初始值
initval;
    n = n_ss;
    e = e_ss;
    d = d_ss;
end;

// 稳态计算
steady;

// 检查Blanchard-Kahn条件
check;

// 模型识别性检验
identification;

// 计算脉冲响应函数
stoch_simul(order=1, irf=40, periods=200);

// 输出识别性检验结果
disp('=== 模型识别性检验完成 ===');
disp('请查看上述identification命令的输出结果：');
disp('1. 如果所有参数都是可识别的，会显示"All parameters are identified"');
disp('2. 如果存在不可识别的参数，会列出具体的参数名称');
disp('3. 检查特征值是否满足稳定性条件');
