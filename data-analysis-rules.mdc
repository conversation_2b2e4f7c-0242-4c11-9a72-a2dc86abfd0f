---
description: 
globs: *.py,*.R,*.ado,*.do,*.m
alwaysApply: false
---
## Cursor Rules for AI (to Generate Clear and Readable Data Analysis Code)
**Preamble:** *When generating data analysis code, adhere to the following rules to ensure clarity, readability, and maintainability for the human user. Strictly follow instructions, only executing the most direct and necessary operations.*
**1. Core Principles for Code Generation:**
    *   **Prioritize Human Readability:** Generate code that is easy for a human to read and understand. Use clear logic and structure.
    *   **Modularity & DRY (Don't Repeat Yourself):** Generate small, well-defined functions or classes to encapsulate reusable logic and avoid code duplication.
**2. Code Formatting:**
    *   Strictly adhere to the specified language's idiomatic style guide (e.g., PEP 8 for Python, Tidyverse style for R).
    *   Ensure consistent indentation, spacing, and structure throughout the generated code.
**3. Naming Conventions:**
    *   Generate **clear, descriptive, and idiomatic names** for all variables, functions, classes, and files. Names must reflect their content or purpose.
**4. Comments & Documentation:**
    *   **Function/Class/Script Header:** For *every generated* custom function, class, or significant script block, include a concise header (using the language's comment syntax) explaining:
        *   Purpose: What it does.
        *   Inputs: Required parameters and their expected types/structures.
        *   Outputs: What it returns or produces.
    *   **Explain Non-Obvious Logic:** Add comments to explain the 'why' behind complex or non-obvious code sections, not just restate what the code does.
    *   **Section Delineation:** Use block comments (e.g., `# --- SECTION NAME ---`) to delineate major logical stages of the analysis.
**5. Code Structure & Dependencies:**
    *   **Imports First:** Place all library/module import statements at the top of the script.
    *   **Constants Definition:** Define script-level constants and configuration parameters (e.g., file paths, thresholds) early and clearly.
    *   **Logical Flow:** Structure the generated analysis code in logical stages (e.g., Data Loading, Cleaning, Analysis, Visualization, Output).
**6. Function & Class Design:**
    *   **Single Responsibility Principle (SRP):** Ensure generated functions and classes each perform one specific, well-defined task.
    *   **Conciseness:** Keep generated functions and methods relatively short and focused.
    *   **Class Usage:** Generate a class when:
        *   Multiple functions need to share and modify the same state/data.
        *   Data and the operations on that data are tightly coupled (e.g., a `DataProcessor` object).
    *   **Type Hinting (Python):** For Python code, always include type hints for function/method parameters and return values. For other languages (R, MATLAB, Stata), use comments to specify expected types if it enhances clarity.
**7. Data Manipulation Best Practices:**
    *   Employ language-specific best practices for efficient and clear data manipulation:
        *   **Python (Pandas):** Prioritize vectorized operations over loops. Use `.pipe()` for cleaner long method chains. Use `.copy()` explicitly when intending to modify a slice/copy to avoid `SettingWithCopyWarning`.
        *   **R (dplyr):** Use piping (`%>%`) for sequential operations, but break very long chains into logical intermediate steps. Leverage vectorized functions.
    *   **Explicit Transformations:** Make all data transformations explicit and easy to follow.
    *   **Intermediate Checks (Optional but good):** Suggest or include (if appropriate for the task length) printing summaries, dimensions, or head/tail of data structures after significant transformations.
**8. Visualizations:**
    *   Ensure all generated plots include:
        *   A clear and descriptive `title`.
        *   `X-axis label`.
        *   `Y-axis label`.
        *   A `legend` if multiple data series are plotted.
    *   Select an appropriate chart type for the data and the insight to be conveyed.
**9. Error Handling:**
    *   Implement robust error handling for anticipated issues (e.g., file not found, invalid data, division by zero) using language-specific constructs (e.g., `try-except` in Python, `tryCatch()` in R, `capture` in Stata).
**10. Avoid Magic Numbers/Strings:**
    *   Define all "magic numbers" (unexplained literals) or hardcoded strings (that might change or need explanation) as named constants at an appropriate scope.
**11. Assertions & Sanity Checks:**
    *   Where appropriate, incorporate assertions or simple sanity checks (e.g., `assert df['id'].is_unique`) after critical data transformations or within functions to help validate data integrity or logic.
    *   If requested to generate testable code, provide stubs for unit tests.
