function [y, T, residual, g1] = static_1(y, x, params, sparse_rowval, sparse_colval, sparse_colptr, T)
residual=NaN(3, 1);
  residual(1)=(y(1))-(y(1)*0.8+0.3*y(2)+0.2*y(3)+x(1));
  residual(2)=(y(3))-(y(3)*0.6+y(2)*0.4+y(1)*0.2+x(3));
  residual(3)=(y(2))-(y(2)*0.7+y(1)*0.3+y(3)*0.1+x(2));
if nargout > 3
    g1_v = NaN(9, 1);
g1_v(1)=(-0.3);
g1_v(2)=(-0.4);
g1_v(3)=0.3;
g1_v(4)=(-0.2);
g1_v(5)=0.4;
g1_v(6)=(-0.1);
g1_v(7)=0.2;
g1_v(8)=(-0.2);
g1_v(9)=(-0.3);
    if ~isoctave && matlab_ver_less_than('9.8')
        sparse_rowval = double(sparse_rowval);
        sparse_colval = double(sparse_colval);
    end
    g1 = sparse(sparse_rowval, sparse_colval, g1_v, 3, 3);
end
end
