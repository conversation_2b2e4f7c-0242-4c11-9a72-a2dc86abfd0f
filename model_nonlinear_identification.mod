// Dynare模型文件：非线性模型识别性检验
// 基于您提供的三方程均衡模型

// 声明变量
var n, e, d;           // 内生变量：生育率、教育水平、消费

// 声明参数
parameters Phi, varphi, epsilon, psi, mu, v_n, v_e, alpha, beta;

// 参数赋值（基准校准值）
Phi = 0.15;       // 子女抚育固定成本
varphi = 0.08;    // 教育成本系数
epsilon = 1.8;    // 教育成本弹性
psi = 0.05;       // 父母赡养成本系数
mu = 1.2;         // 父母赡养成本弹性
v_n = 1.2;        // 生育偏好参数
v_e = 1.5;        // 教育偏好参数
alpha = 0.35;     // 效用函数参数
beta = 0.96;      // 贴现因子

// 模型方程（非线性形式）
model;
    // 方程1：第一个最优化条件
    (1 - e(-1) - d) / (beta * d) * (1 - (Phi + varphi * e^epsilon) * n - psi * n(-1)^mu / mu) = 
    epsilon / v_e * varphi * e^epsilon * n * d;
    
    // 方程2：第二个最优化条件
    d * ((1 + beta) * d - 1 + e(-1)) = 
    alpha / (1 - alpha) * mu / (psi * n^mu) * ((1 + beta) * (1 - e(-1)) - (1 + 2*beta) * d);
    
    // 方程3：第三个最优化条件
    (Phi + (1 - v_n/v_e * epsilon) * varphi * e^epsilon) * d = 
    ((1 + beta) * (1 - e(-1)) - (1 + 2*beta) * d) / beta * 
    (1 - (Phi + varphi * e^epsilon) * n - psi * n(-1)^mu / mu) * mu / n;
end;

// 初始值设定（寻找合理的稳态）
initval;
    n = 0.4;
    e = 0.25;
    d = 0.35;
end;

// 计算稳态
steady(solve_algo=4, maxit=1000);

// 检查模型的动态性质
check;

// 模型识别性检验
identification(advanced=1);

// 输出详细的识别性分析
disp('=== 详细识别性分析 ===');

// 检查雅可比矩阵的条件数
disp('正在检查雅可比矩阵的条件数...');

// 参数敏感性测试
disp('=== 参数敏感性测试 ===');

// 保存原始参数
beta_orig = beta;
alpha_orig = alpha;
epsilon_orig = epsilon;

// 测试关键参数的识别性
param_tests = [0.90, 0.95, 0.96, 0.97, 0.98];
for i = 1:length(param_tests)
    beta = param_tests(i);
    try
        steady(solve_algo=4);
        identification(silent);
        fprintf('beta = %.2f: 识别性检验通过\n', beta);
    catch
        fprintf('beta = %.2f: 识别性存在问题\n', beta);
    end
end

// 恢复原值并测试alpha
beta = beta_orig;
alpha_tests = [0.25, 0.30, 0.35, 0.40, 0.45];
for i = 1:length(alpha_tests)
    alpha = alpha_tests(i);
    try
        steady(solve_algo=4);
        identification(silent);
        fprintf('alpha = %.2f: 识别性检验通过\n', alpha);
    catch
        fprintf('alpha = %.2f: 识别性存在问题\n', alpha);
    end
end

// 恢复原值并测试epsilon
alpha = alpha_orig;
epsilon_tests = [1.5, 1.8, 2.0, 2.2, 2.5];
for i = 1:length(epsilon_tests)
    epsilon = epsilon_tests(i);
    try
        steady(solve_algo=4);
        identification(silent);
        fprintf('epsilon = %.2f: 识别性检验通过\n', epsilon);
    catch
        fprintf('epsilon = %.2f: 识别性存在问题\n', epsilon);
    end
end

// 恢复所有原始参数值
beta = beta_orig;
alpha = alpha_orig;
epsilon = epsilon_orig;

// 最终识别性检验
disp('=== 最终识别性检验 ===');
steady(solve_algo=4);
identification(advanced=1);

disp('=== 检验结果解释 ===');
disp('1. 如果identification命令成功执行，说明模型在当前参数化下是局部可识别的');
disp('2. 注意检查是否有参数显示为"不可识别"或"弱识别"');
disp('3. 检查特征值是否满足稳定性条件（模数小于1）');
disp('4. 如果存在问题，可能需要：');
disp('   - 调整参数值');
disp('   - 简化模型结构');
disp('   - 增加更多的观测方程');
disp('   - 检查模型的理论基础');
