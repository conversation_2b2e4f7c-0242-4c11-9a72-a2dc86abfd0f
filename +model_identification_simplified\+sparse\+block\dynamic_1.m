function [y, T, residual, g1] = dynamic_1(y, x, params, steady_state, sparse_rowval, sparse_colval, sparse_colptr, T)
residual=NaN(1, 1);
  y(6)=0.6*y(3)+0.4*y(2)+y(4)*0.2+x(3);
  y(5)=y(2)*0.7+y(4)*0.3+y(6)*0.1+x(2);
  residual(1)=(y(4))-(0.8*y(1)+0.3*y(5)+0.2*y(6)+x(1));
if nargout > 3
    g1_v = NaN(1, 1);
g1_v(1)=0.864;
    if ~isoctave && matlab_ver_less_than('9.8')
        sparse_rowval = double(sparse_rowval);
        sparse_colval = double(sparse_colval);
    end
    g1 = sparse(sparse_rowval, sparse_colval, g1_v, 1, 1);
end
end
