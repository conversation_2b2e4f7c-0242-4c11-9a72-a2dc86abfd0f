%
% Status : main Dynare file
%
% Warning : this file is generated automatically by Dynare
%           from model file (.mod)

tic0 = tic;
% Define global variables.
global M_ options_ oo_ estim_params_ bayestopt_ dataset_ dataset_info estimation_info
options_ = [];
M_.fname = 'model_identification_simplified';
M_.dynare_version = '6.3';
oo_.dynare_version = '6.3';
options_.dynare_version = '6.3';
%
% Some global variables initialization
%
global_initialization;
M_.exo_names = cell(3,1);
M_.exo_names_tex = cell(3,1);
M_.exo_names_long = cell(3,1);
M_.exo_names(1) = {'eps_n'};
M_.exo_names_tex(1) = {'eps\_n'};
M_.exo_names_long(1) = {'eps_n'};
M_.exo_names(2) = {'eps_e'};
M_.exo_names_tex(2) = {'eps\_e'};
M_.exo_names_long(2) = {'eps_e'};
M_.exo_names(3) = {'eps_d'};
M_.exo_names_tex(3) = {'eps\_d'};
M_.exo_names_long(3) = {'eps_d'};
M_.endo_names = cell(3,1);
M_.endo_names_tex = cell(3,1);
M_.endo_names_long = cell(3,1);
M_.endo_names(1) = {'n'};
M_.endo_names_tex(1) = {'n'};
M_.endo_names_long(1) = {'n'};
M_.endo_names(2) = {'e'};
M_.endo_names_tex(2) = {'e'};
M_.endo_names_long(2) = {'e'};
M_.endo_names(3) = {'d'};
M_.endo_names_tex(3) = {'d'};
M_.endo_names_long(3) = {'d'};
M_.endo_partitions = struct();
M_.param_names = cell(12,1);
M_.param_names_tex = cell(12,1);
M_.param_names_long = cell(12,1);
M_.param_names(1) = {'Phi'};
M_.param_names_tex(1) = {'Phi'};
M_.param_names_long(1) = {'Phi'};
M_.param_names(2) = {'varphi'};
M_.param_names_tex(2) = {'varphi'};
M_.param_names_long(2) = {'varphi'};
M_.param_names(3) = {'epsilon'};
M_.param_names_tex(3) = {'epsilon'};
M_.param_names_long(3) = {'epsilon'};
M_.param_names(4) = {'psi'};
M_.param_names_tex(4) = {'psi'};
M_.param_names_long(4) = {'psi'};
M_.param_names(5) = {'mu'};
M_.param_names_tex(5) = {'mu'};
M_.param_names_long(5) = {'mu'};
M_.param_names(6) = {'v_n'};
M_.param_names_tex(6) = {'v\_n'};
M_.param_names_long(6) = {'v_n'};
M_.param_names(7) = {'v_e'};
M_.param_names_tex(7) = {'v\_e'};
M_.param_names_long(7) = {'v_e'};
M_.param_names(8) = {'alpha'};
M_.param_names_tex(8) = {'alpha'};
M_.param_names_long(8) = {'alpha'};
M_.param_names(9) = {'beta'};
M_.param_names_tex(9) = {'beta'};
M_.param_names_long(9) = {'beta'};
M_.param_names(10) = {'n_ss'};
M_.param_names_tex(10) = {'n\_ss'};
M_.param_names_long(10) = {'n_ss'};
M_.param_names(11) = {'e_ss'};
M_.param_names_tex(11) = {'e\_ss'};
M_.param_names_long(11) = {'e_ss'};
M_.param_names(12) = {'d_ss'};
M_.param_names_tex(12) = {'d\_ss'};
M_.param_names_long(12) = {'d_ss'};
M_.param_partitions = struct();
M_.exo_det_nbr = 0;
M_.exo_nbr = 3;
M_.endo_nbr = 3;
M_.param_nbr = 12;
M_.orig_endo_nbr = 3;
M_.aux_vars = [];
M_.Sigma_e = zeros(3, 3);
M_.Correlation_matrix = eye(3, 3);
M_.H = 0;
M_.Correlation_matrix_ME = 1;
M_.sigma_e_is_diagonal = true;
M_.det_shocks = [];
M_.surprise_shocks = [];
M_.learnt_shocks = [];
M_.learnt_endval = [];
M_.heteroskedastic_shocks.Qvalue_orig = [];
M_.heteroskedastic_shocks.Qscale_orig = [];
M_.matched_irfs = {};
M_.matched_irfs_weights = {};
options_.linear = true;
options_.block = false;
options_.bytecode = false;
options_.use_dll = false;
options_.ramsey_policy = false;
options_.discretionary_policy = false;
M_.nonzero_hessian_eqs = [];
M_.hessian_eq_zero = isempty(M_.nonzero_hessian_eqs);
M_.eq_nbr = 3;
M_.ramsey_orig_eq_nbr = 0;
M_.ramsey_orig_endo_nbr = 0;
M_.set_auxiliary_variables = exist(['./+' M_.fname '/set_auxiliary_variables.m'], 'file') == 2;
M_.epilogue_names = {};
M_.epilogue_var_list_ = {};
M_.orig_maximum_endo_lag = 1;
M_.orig_maximum_endo_lead = 0;
M_.orig_maximum_exo_lag = 0;
M_.orig_maximum_exo_lead = 0;
M_.orig_maximum_exo_det_lag = 0;
M_.orig_maximum_exo_det_lead = 0;
M_.orig_maximum_lag = 1;
M_.orig_maximum_lead = 0;
M_.orig_maximum_lag_with_diffs_expanded = 1;
M_.lead_lag_incidence = [
 1 4;
 2 5;
 3 6;]';
M_.nstatic = 0;
M_.nfwrd   = 0;
M_.npred   = 3;
M_.nboth   = 0;
M_.nsfwrd   = 0;
M_.nspred   = 3;
M_.ndynamic   = 3;
M_.dynamic_tmp_nbr = [0; 0; 0; 0; ];
M_.equations_tags = {
  1 , 'name' , 'n' ;
  2 , 'name' , 'd' ;
  3 , 'name' , 'e' ;
};
M_.mapping.n.eqidx = [1 2 3 ];
M_.mapping.e.eqidx = [1 2 3 ];
M_.mapping.d.eqidx = [1 2 3 ];
M_.mapping.eps_n.eqidx = [1 ];
M_.mapping.eps_e.eqidx = [3 ];
M_.mapping.eps_d.eqidx = [2 ];
M_.static_and_dynamic_models_differ = false;
M_.has_external_function = false;
M_.block_structure.time_recursive = true;
M_.block_structure.block(1).Simulation_Type = 6;
M_.block_structure.block(1).endo_nbr = 3;
M_.block_structure.block(1).mfs = 1;
M_.block_structure.block(1).equation = [ 2 3 1];
M_.block_structure.block(1).variable = [ 3 2 1];
M_.block_structure.block(1).is_linear = true;
M_.block_structure.block(1).NNZDerivatives = 3;
M_.block_structure.block(1).bytecode_jacob_cols_to_sparse = [0 0 0 0 0 1 ];
M_.block_structure.block(1).g1_sparse_rowval = int32([1 ]);
M_.block_structure.block(1).g1_sparse_colval = int32([1 ]);
M_.block_structure.block(1).g1_sparse_colptr = int32([1 2 ]);
M_.block_structure.variable_reordered = [ 3 2 1];
M_.block_structure.equation_reordered = [ 2 3 1];
M_.block_structure.incidence(1).lead_lag = -1;
M_.block_structure.incidence(1).sparse_IM = [
 1 1;
 2 2;
 2 3;
 3 2;
];
M_.block_structure.incidence(2).lead_lag = 0;
M_.block_structure.incidence(2).sparse_IM = [
 1 1;
 1 2;
 1 3;
 2 1;
 2 3;
 3 1;
 3 2;
 3 3;
];
M_.block_structure.dyn_tmp_nbr = 0;
M_.state_var = [3 2 1 ];
M_.maximum_lag = 1;
M_.maximum_lead = 0;
M_.maximum_endo_lag = 1;
M_.maximum_endo_lead = 0;
oo_.steady_state = zeros(3, 1);
M_.maximum_exo_lag = 0;
M_.maximum_exo_lead = 0;
oo_.exo_steady_state = zeros(3, 1);
M_.params = NaN(12, 1);
M_.endo_trends = struct('deflator', cell(3, 1), 'log_deflator', cell(3, 1), 'growth_factor', cell(3, 1), 'log_growth_factor', cell(3, 1));
M_.NNZDerivatives = [15; 0; -1; ];
M_.dynamic_g1_sparse_rowval = int32([1 2 3 2 1 2 3 1 3 1 2 3 1 3 2 ]);
M_.dynamic_g1_sparse_colval = int32([1 2 2 3 4 4 4 5 5 6 6 6 10 11 12 ]);
M_.dynamic_g1_sparse_colptr = int32([1 2 4 5 8 10 13 13 13 13 14 15 16 ]);
M_.dynamic_g2_sparse_indices = int32([]);
M_.lhs = {
'n'; 
'd'; 
'e'; 
};
M_.static_tmp_nbr = [0; 0; 0; 0; ];
M_.block_structure_stat.block(1).Simulation_Type = 6;
M_.block_structure_stat.block(1).endo_nbr = 3;
M_.block_structure_stat.block(1).mfs = 3;
M_.block_structure_stat.block(1).equation = [ 1 2 3];
M_.block_structure_stat.block(1).variable = [ 2 3 1];
M_.block_structure_stat.variable_reordered = [ 2 3 1];
M_.block_structure_stat.equation_reordered = [ 1 2 3];
M_.block_structure_stat.incidence.sparse_IM = [
 1 1;
 1 2;
 1 3;
 2 1;
 2 2;
 2 3;
 3 1;
 3 2;
 3 3;
];
M_.block_structure_stat.tmp_nbr = 0;
M_.block_structure_stat.block(1).g1_sparse_rowval = int32([1 2 3 1 2 3 1 2 3 ]);
M_.block_structure_stat.block(1).g1_sparse_colval = int32([1 1 1 2 2 2 3 3 3 ]);
M_.block_structure_stat.block(1).g1_sparse_colptr = int32([1 4 7 10 ]);
M_.static_g1_sparse_rowval = int32([1 2 3 1 2 3 1 2 3 ]);
M_.static_g1_sparse_colval = int32([1 1 1 2 2 2 3 3 3 ]);
M_.static_g1_sparse_colptr = int32([1 4 7 10 ]);
M_.static_g2_sparse_indices = int32([]);
M_.params(1) = 0.1;
Phi = M_.params(1);
M_.params(2) = 0.05;
varphi = M_.params(2);
M_.params(3) = 2.0;
epsilon = M_.params(3);
M_.params(4) = 0.02;
psi = M_.params(4);
M_.params(5) = 1.5;
mu = M_.params(5);
M_.params(6) = 1.0;
v_n = M_.params(6);
M_.params(7) = 1.0;
v_e = M_.params(7);
M_.params(8) = 0.3;
alpha = M_.params(8);
M_.params(9) = 0.95;
beta = M_.params(9);
M_.params(10) = 0.5;
n_ss = M_.params(10);
M_.params(11) = 0.3;
e_ss = M_.params(11);
M_.params(12) = 0.4;
d_ss = M_.params(12);
%
% SHOCKS instructions
%
M_.exo_det_length = 0;
M_.Sigma_e(1, 1) = 0.01;
M_.Sigma_e(2, 2) = 0.01;
M_.Sigma_e(3, 3) = 0.01;
%
% INITVAL instructions
%
options_.initval_file = false;
oo_.steady_state(1) = M_.params(10);
oo_.steady_state(2) = M_.params(11);
oo_.steady_state(3) = M_.params(12);
if M_.exo_nbr > 0
	oo_.exo_simul = ones(M_.maximum_lag,1)*oo_.exo_steady_state';
end
if M_.exo_det_nbr > 0
	oo_.exo_det_simul = ones(M_.maximum_lag,1)*oo_.exo_det_steady_state';
end
steady;
oo_.dr.eigval = check(M_,options_,oo_);
options_ident = struct();
identification.run(M_,oo_,options_,bayestopt_,estim_params_,options_ident);
options_.irf = 40;
options_.order = 1;
options_.periods = 200;
var_list_ = {};
[info, oo_, options_, M_] = stoch_simul(M_, options_, oo_, var_list_);
disp('=== 模型识别性检验完成 ===');
disp('请查看上述identification命令的输出结果：');
disp('1. 如果所有参数都是可识别的，会显示"All parameters are identified"');
disp('2. 如果存在不可识别的参数，会列出具体的参数名称');
disp('3. 检查特征值是否满足稳定性条件');


oo_.time = toc(tic0);
disp(['Total computing time : ' dynsec2hms(oo_.time) ]);
if ~exist([M_.dname filesep 'Output'],'dir')
    mkdir(M_.dname,'Output');
end
save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'oo_', 'M_', 'options_');
if exist('estim_params_', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'estim_params_', '-append');
end
if exist('bayestopt_', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'bayestopt_', '-append');
end
if exist('dataset_', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'dataset_', '-append');
end
if exist('estimation_info', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'estimation_info', '-append');
end
if exist('dataset_info', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'dataset_info', '-append');
end
if exist('oo_recursive_', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'oo_recursive_', '-append');
end
if exist('options_mom_', 'var') == 1
  save([M_.dname filesep 'Output' filesep 'model_identification_simplified_results.mat'], 'options_mom_', '-append');
end
if ~isempty(lastwarn)
  disp('Note: warning(s) encountered in MATLAB/Octave code')
end
