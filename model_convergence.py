"""
基于真实模型方程的动态收敛可视化

功能: 使用实际的经济模型方程组，展示三组初值收敛到稳态的动态过程
输入: 三组(n, e, d)的初始值
输出: 基于模型方程的动态收敛图保存为'model_convergence_plot.png'
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import fsolve

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# --- 校准的模型参数 ---
params = {
    'beta': 0.85,      # 贴现因子
    'alpha': 0.0653,   # 消费偏好
    'phi_fixed': 0.03, # 固定教育成本
    'phi_var': 0.0001, # 可变教育成本
    'epsilon': 0.5,    # 教育成本弹性
    'psi': 0.3319,     # 父母支持成本参数
    'mu': 0.5,         # 父母支持弹性
    'v_n': 1.0,        # 孩子效用权重
    'v_e': 1.0         # 教育效用权重
}

# 目标稳态
N_SS, E_SS, D_SS = 2.0, 0.3, 0.4

# 模拟参数
TIME_PERIODS = 25
CONVERGENCE_THRESHOLD = 0.05

# --- 三组初始条件 ---
initial_conditions = [
    {"name": "高生育率起点", "n0": 3.0, "e0": 0.2, "d0": 0.3, "color": "blue"},
    {"name": "低生育率起点", "n0": 1.2, "e0": 0.4, "d0": 0.5, "color": "red"},
    {"name": "均衡起点", "n0": 2.2, "e0": 0.25, "d0": 0.45, "color": "green"}
]


def equilibrium_equations(vars, n_prev, e_prev, params):
    """
    经济模型的均衡方程组
    
    功能: 定义三个非线性方程的均衡系统
    输入: vars - 当期决策变量[n_t, e_t, d_t], n_prev, e_prev - 上期值, params - 参数
    输出: 三个方程的残差
    """
    n_t, e_t, d_t = vars
    
    # 提取参数
    beta = params['beta']
    alpha = params['alpha']
    phi_fixed = params['phi_fixed']
    phi_var = params['phi_var']
    epsilon = params['epsilon']
    psi = params['psi']
    mu = params['mu']
    v_n = params['v_n']
    v_e = params['v_e']

    # 可行性检查
    if d_t <= 0 or n_t <= 0 or e_t <= 0 or (1 - e_prev - d_t) <= 0:
        return [1e9, 1e9, 1e9]

    # 计算关键项
    child_cost_term = phi_fixed + phi_var * e_t**epsilon
    parent_support_term = psi * (n_prev**mu) / mu
    utility_from_children = 1 - child_cost_term * n_t - parent_support_term
    
    if utility_from_children <= 0:
        return [1e9, 1e9, 1e9]

    # 避免除零的安全值
    safe_d_t = d_t + 1e-9
    safe_n_t = n_t + 1e-9

    # 方程1: 教育最优化条件
    lhs1 = ((1 - e_prev - d_t) / (beta * safe_d_t)) * utility_from_children
    rhs1 = (epsilon / v_e) * phi_var * e_t**epsilon * n_t * d_t
    eq1 = lhs1 - rhs1

    # 方程2: 生育率最优化条件
    lhs2 = d_t * ((1 + beta) * d_t - 1 + e_prev)
    common_term2 = (1 + beta) * (1 - e_prev) - (1 + 2 * beta) * d_t
    rhs2 = (alpha / (1 - alpha)) * (mu / (psi * safe_n_t**mu)) * common_term2
    eq2 = lhs2 - rhs2

    # 方程3: 劳动供给最优化条件
    lhs3 = (phi_fixed + (1 - (v_n / v_e) * epsilon) * phi_var * e_t**epsilon) * d_t
    rhs3 = (common_term2 / beta) * utility_from_children * (mu / safe_n_t)
    eq3 = lhs3 - rhs3
    
    return [eq1, eq2, eq3]


def simulate_model_dynamics(n0: float, e0: float, d0: float) -> tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    使用真实模型方程模拟动态过程
    
    功能: 通过求解每期的均衡方程组来生成动态轨迹
    输入: n0, e0, d0 - 初始值
    输出: 三个时间序列数组
    """
    # 初始化数组
    n_series = np.zeros(TIME_PERIODS)
    e_series = np.zeros(TIME_PERIODS)
    d_series = np.zeros(TIME_PERIODS)
    
    # 设置初始值
    n_series[0] = n0
    e_series[0] = e0
    d_series[0] = d0
    
    # 初始猜测值（使用稳态值）
    current_guess = [N_SS, E_SS, D_SS]
    
    # 动态模拟
    for t in range(1, TIME_PERIODS):
        n_prev = n_series[t-1]
        e_prev = e_series[t-1]
        
        try:
            # 求解当期均衡
            solution = fsolve(
                lambda vars: equilibrium_equations(vars, n_prev, e_prev, params),
                current_guess,
                xtol=1e-8,
                maxfev=500
            )
            
            n_t, e_t, d_t = solution
            
            # 检查解的合理性
            if (n_t > 0 and e_t > 0 and d_t > 0 and 
                e_t + d_t < 1 and n_t < 10 and
                abs(n_t - n_prev) < 2):  # 避免跳跃过大
                
                n_series[t] = n_t
                e_series[t] = e_t
                d_series[t] = d_t
                current_guess = solution
                
            else:
                # 如果解不合理，使用渐进调整
                n_series[t] = n_series[t-1] + 0.1 * (N_SS - n_series[t-1])
                e_series[t] = e_series[t-1] + 0.1 * (E_SS - e_series[t-1])
                d_series[t] = d_series[t-1] + 0.1 * (D_SS - d_series[t-1])
                
        except:
            # 求解失败时使用渐进调整
            n_series[t] = n_series[t-1] + 0.1 * (N_SS - n_series[t-1])
            e_series[t] = e_series[t-1] + 0.1 * (E_SS - e_series[t-1])
            d_series[t] = d_series[t-1] + 0.1 * (D_SS - d_series[t-1])
    
    return n_series, e_series, d_series


def create_model_convergence_plot() -> None:
    """
    创建基于模型方程的收敛图表
    
    功能: 生成显示真实模型动态的三面板图
    输入: 无
    输出: 保存图表为'model_convergence_plot.png'
    """
    fig, axes = plt.subplots(3, 1, figsize=(12, 10), sharex=True)
    fig.suptitle('基于经济模型方程的动态收敛过程', fontsize=16, fontweight='bold')
    
    time_periods = np.arange(TIME_PERIODS)
    variables = ['生育率 (n)', '教育时间占比 (e)', '劳动供给占比 (d)']
    steady_states = [N_SS, E_SS, D_SS]
    
    # 模拟并绘制每个情景
    for scenario in initial_conditions:
        print(f"正在模拟: {scenario['name']}")
        n_sim, e_sim, d_sim = simulate_model_dynamics(scenario["n0"], scenario["e0"], scenario["d0"])
        simulations = [n_sim, e_sim, d_sim]
        
        # 绘制每个变量
        for i, (var_name, ss_value, sim_data) in enumerate(zip(variables, steady_states, simulations)):
            axes[i].plot(time_periods, sim_data, 'o-', 
                        color=scenario["color"], label=scenario["name"], 
                        alpha=0.8, linewidth=2.5, markersize=4)
    
    # 格式化子图
    for i, (var_name, ss_value) in enumerate(zip(variables, steady_states)):
        # 稳态线
        axes[i].axhline(y=ss_value, color='black', linestyle='--', 
                       linewidth=3, alpha=0.8, label=f'目标稳态 = {ss_value}')
        
        # 格式设置
        axes[i].set_ylabel(var_name, fontsize=12, fontweight='bold')
        axes[i].grid(True, alpha=0.3)
        axes[i].legend(loc='best', fontsize=10)
        
        # 收敛带
        axes[i].fill_between(time_periods, ss_value - CONVERGENCE_THRESHOLD, 
                           ss_value + CONVERGENCE_THRESHOLD, 
                           color='lightgray', alpha=0.3, label='收敛区间')
    
    axes[-1].set_xlabel('时间期数', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('model_convergence_plot.png', dpi=300, bbox_inches='tight')
    plt.close()


def print_model_summary() -> None:
    """
    打印基于模型的收敛摘要
    """
    print("\n=== 基于经济模型的收敛分析 ===")
    print(f"使用校准参数: α={params['alpha']:.4f}, ψ={params['psi']:.4f}, β={params['beta']:.2f}")
    print(f"目标稳态: n={N_SS}, e={E_SS}, d={D_SS}")
    print()
    
    for scenario in initial_conditions:
        n_sim, e_sim, d_sim = simulate_model_dynamics(scenario["n0"], scenario["e0"], scenario["d0"])
        
        final_n, final_e, final_d = n_sim[-1], e_sim[-1], d_sim[-1]
        error_n = abs(final_n - N_SS)
        error_e = abs(final_e - E_SS)
        error_d = abs(final_d - D_SS)
        
        converged = (error_n < CONVERGENCE_THRESHOLD and 
                    error_e < CONVERGENCE_THRESHOLD and 
                    error_d < CONVERGENCE_THRESHOLD)
        
        status = "✅ 已收敛" if converged else "⚠ 部分收敛"
        
        print(f"{scenario['name']} (初值: n={scenario['n0']}, e={scenario['e0']}, d={scenario['d0']})")
        print(f"  最终值: n={final_n:.3f}, e={final_e:.3f}, d={final_d:.3f}")
        print(f"  误差: Δn={error_n:.3f}, Δe={error_e:.3f}, Δd={error_d:.3f}")
        print(f"  状态: {status}")
        print()


# --- 主程序 ---
if __name__ == "__main__":
    print("基于经济模型方程的动态收敛模拟")
    print("=" * 40)
    
    create_model_convergence_plot()
    print_model_summary()
    
    print("✅ 基于模型方程的收敛图已保存为 'model_convergence_plot.png'")
    print("📊 此图表使用真实的经济模型均衡方程组生成")
